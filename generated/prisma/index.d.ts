
/**
 * Client
**/

import * as runtime from './runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model OlvaTrackings
 * 
 */
export type OlvaTrackings = $Result.DefaultSelection<Prisma.$OlvaTrackingsPayload>

/**
 * ##  Prisma Client ʲˢ
 *
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more OlvaTrackings
 * const olvaTrackings = await prisma.olvaTrackings.findMany()
 * ```
 *
 *
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  const U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   *
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more OlvaTrackings
   * const olvaTrackings = await prisma.olvaTrackings.findMany()
   * ```
   *
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): PrismaClient;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   *
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb<ClientOptions>, ExtArgs, $Utils.Call<Prisma.TypeMapCb<ClientOptions>, {
    extArgs: ExtArgs
  }>>

      /**
   * `prisma.olvaTrackings`: Exposes CRUD operations for the **OlvaTrackings** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more OlvaTrackings
    * const olvaTrackings = await prisma.olvaTrackings.findMany()
    * ```
    */
  get olvaTrackings(): Prisma.OlvaTrackingsDelegate<ExtArgs, ClientOptions>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 6.13.0
   * Query Engine version: 361e86d0ea4987e9f53a565309b3eed797a6bcbd
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    *
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    *
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   *
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? P : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    OlvaTrackings: 'OlvaTrackings'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb<ClientOptions = {}> extends $Utils.Fn<{extArgs: $Extensions.InternalArgs }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], ClientOptions extends { omit: infer OmitOptions } ? OmitOptions : {}>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> = {
    globalOmitOptions: {
      omit: GlobalOmitOptions
    }
    meta: {
      modelProps: "olvaTrackings"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      OlvaTrackings: {
        payload: Prisma.$OlvaTrackingsPayload<ExtArgs>
        fields: Prisma.OlvaTrackingsFieldRefs
        operations: {
          findUnique: {
            args: Prisma.OlvaTrackingsFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.OlvaTrackingsFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>
          }
          findFirst: {
            args: Prisma.OlvaTrackingsFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.OlvaTrackingsFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>
          }
          findMany: {
            args: Prisma.OlvaTrackingsFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>[]
          }
          create: {
            args: Prisma.OlvaTrackingsCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>
          }
          createMany: {
            args: Prisma.OlvaTrackingsCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.OlvaTrackingsCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>[]
          }
          delete: {
            args: Prisma.OlvaTrackingsDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>
          }
          update: {
            args: Prisma.OlvaTrackingsUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>
          }
          deleteMany: {
            args: Prisma.OlvaTrackingsDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.OlvaTrackingsUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateManyAndReturn: {
            args: Prisma.OlvaTrackingsUpdateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>[]
          }
          upsert: {
            args: Prisma.OlvaTrackingsUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$OlvaTrackingsPayload>
          }
          aggregate: {
            args: Prisma.OlvaTrackingsAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateOlvaTrackings>
          }
          groupBy: {
            args: Prisma.OlvaTrackingsGroupByArgs<ExtArgs>
            result: $Utils.Optional<OlvaTrackingsGroupByOutputType>[]
          }
          count: {
            args: Prisma.OlvaTrackingsCountArgs<ExtArgs>
            result: $Utils.Optional<OlvaTrackingsCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Shorthand for `emit: 'stdout'`
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events only
     * log: [
     *   { emit: 'event', level: 'query' },
     *   { emit: 'event', level: 'info' },
     *   { emit: 'event', level: 'warn' }
     *   { emit: 'event', level: 'error' }
     * ]
     * 
     * / Emit as events and log to stdout
     * og: [
     *  { emit: 'stdout', level: 'query' },
     *  { emit: 'stdout', level: 'info' },
     *  { emit: 'stdout', level: 'warn' }
     *  { emit: 'stdout', level: 'error' }
     * 
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
    /**
     * Global configuration for omitting model fields by default.
     * 
     * @example
     * ```
     * const prisma = new PrismaClient({
     *   omit: {
     *     user: {
     *       password: true
     *     }
     *   }
     * })
     * ```
     */
    omit?: Prisma.GlobalOmitConfig
  }
  export type GlobalOmitConfig = {
    olvaTrackings?: OlvaTrackingsOmit
  }

  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type CheckIsLogLevel<T> = T extends LogLevel ? T : never;

  export type GetLogType<T> = CheckIsLogLevel<
    T extends LogDefinition ? T['level'] : T
  >;

  export type GetEvents<T extends any[]> = T extends Array<LogLevel | LogDefinition>
    ? GetLogType<T[number]>
    : never;

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'updateManyAndReturn'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */



  /**
   * Models
   */

  /**
   * Model OlvaTrackings
   */

  export type AggregateOlvaTrackings = {
    _count: OlvaTrackingsCountAggregateOutputType | null
    _avg: OlvaTrackingsAvgAggregateOutputType | null
    _sum: OlvaTrackingsSumAggregateOutputType | null
    _min: OlvaTrackingsMinAggregateOutputType | null
    _max: OlvaTrackingsMaxAggregateOutputType | null
  }

  export type OlvaTrackingsAvgAggregateOutputType = {
    id: number | null
    id_envio: number | null
    address_id: number | null
    emision: number | null
    tracking: number | null
    cliente: number | null
    status: number | null
    id_operador: number | null
    id_sede: number | null
    id_persona_jur_area: number | null
    created_at: number | null
    updated_at: number | null
    created_by: number | null
    updated_by: number | null
    start_geocode: number | null
  }

  export type OlvaTrackingsSumAggregateOutputType = {
    id: number | null
    id_envio: number | null
    address_id: number | null
    emision: number | null
    tracking: number | null
    cliente: number | null
    status: number | null
    id_operador: number | null
    id_sede: number | null
    id_persona_jur_area: number | null
    created_at: number | null
    updated_at: number | null
    created_by: number | null
    updated_by: number | null
    start_geocode: number | null
  }

  export type OlvaTrackingsMinAggregateOutputType = {
    id: number | null
    id_envio: number | null
    address_id: number | null
    emision: number | null
    tracking: number | null
    address: string | null
    ubigeo: string | null
    cliente: number | null
    orden_servicio: string | null
    fecha_emision: string | null
    geocode: boolean | null
    contenedor: string | null
    servicio_codigo: string | null
    status: number | null
    nombre_cliente: string | null
    documento_client: string | null
    centro_costo: string | null
    id_operador: number | null
    codigo_operador: string | null
    id_sede: number | null
    nombre_sede: string | null
    id_persona_jur_area: number | null
    sector_direccion: string | null
    tracking_tipo: string | null
    created_at: number | null
    updated_at: number | null
    created_by: number | null
    updated_by: number | null
    start_geocode: number | null
    address_normalized: string | null
    address_token: string | null
  }

  export type OlvaTrackingsMaxAggregateOutputType = {
    id: number | null
    id_envio: number | null
    address_id: number | null
    emision: number | null
    tracking: number | null
    address: string | null
    ubigeo: string | null
    cliente: number | null
    orden_servicio: string | null
    fecha_emision: string | null
    geocode: boolean | null
    contenedor: string | null
    servicio_codigo: string | null
    status: number | null
    nombre_cliente: string | null
    documento_client: string | null
    centro_costo: string | null
    id_operador: number | null
    codigo_operador: string | null
    id_sede: number | null
    nombre_sede: string | null
    id_persona_jur_area: number | null
    sector_direccion: string | null
    tracking_tipo: string | null
    created_at: number | null
    updated_at: number | null
    created_by: number | null
    updated_by: number | null
    start_geocode: number | null
    address_normalized: string | null
    address_token: string | null
  }

  export type OlvaTrackingsCountAggregateOutputType = {
    id: number
    id_envio: number
    address_id: number
    emision: number
    tracking: number
    address: number
    ubigeo: number
    cliente: number
    orden_servicio: number
    fecha_emision: number
    geocode: number
    contenedor: number
    servicio_codigo: number
    status: number
    nombre_cliente: number
    documento_client: number
    centro_costo: number
    id_operador: number
    codigo_operador: number
    id_sede: number
    nombre_sede: number
    id_persona_jur_area: number
    sector_direccion: number
    tracking_tipo: number
    created_at: number
    updated_at: number
    created_by: number
    updated_by: number
    start_geocode: number
    address_normalized: number
    address_problems: number
    address_token: number
    _all: number
  }


  export type OlvaTrackingsAvgAggregateInputType = {
    id?: true
    id_envio?: true
    address_id?: true
    emision?: true
    tracking?: true
    cliente?: true
    status?: true
    id_operador?: true
    id_sede?: true
    id_persona_jur_area?: true
    created_at?: true
    updated_at?: true
    created_by?: true
    updated_by?: true
    start_geocode?: true
  }

  export type OlvaTrackingsSumAggregateInputType = {
    id?: true
    id_envio?: true
    address_id?: true
    emision?: true
    tracking?: true
    cliente?: true
    status?: true
    id_operador?: true
    id_sede?: true
    id_persona_jur_area?: true
    created_at?: true
    updated_at?: true
    created_by?: true
    updated_by?: true
    start_geocode?: true
  }

  export type OlvaTrackingsMinAggregateInputType = {
    id?: true
    id_envio?: true
    address_id?: true
    emision?: true
    tracking?: true
    address?: true
    ubigeo?: true
    cliente?: true
    orden_servicio?: true
    fecha_emision?: true
    geocode?: true
    contenedor?: true
    servicio_codigo?: true
    status?: true
    nombre_cliente?: true
    documento_client?: true
    centro_costo?: true
    id_operador?: true
    codigo_operador?: true
    id_sede?: true
    nombre_sede?: true
    id_persona_jur_area?: true
    sector_direccion?: true
    tracking_tipo?: true
    created_at?: true
    updated_at?: true
    created_by?: true
    updated_by?: true
    start_geocode?: true
    address_normalized?: true
    address_token?: true
  }

  export type OlvaTrackingsMaxAggregateInputType = {
    id?: true
    id_envio?: true
    address_id?: true
    emision?: true
    tracking?: true
    address?: true
    ubigeo?: true
    cliente?: true
    orden_servicio?: true
    fecha_emision?: true
    geocode?: true
    contenedor?: true
    servicio_codigo?: true
    status?: true
    nombre_cliente?: true
    documento_client?: true
    centro_costo?: true
    id_operador?: true
    codigo_operador?: true
    id_sede?: true
    nombre_sede?: true
    id_persona_jur_area?: true
    sector_direccion?: true
    tracking_tipo?: true
    created_at?: true
    updated_at?: true
    created_by?: true
    updated_by?: true
    start_geocode?: true
    address_normalized?: true
    address_token?: true
  }

  export type OlvaTrackingsCountAggregateInputType = {
    id?: true
    id_envio?: true
    address_id?: true
    emision?: true
    tracking?: true
    address?: true
    ubigeo?: true
    cliente?: true
    orden_servicio?: true
    fecha_emision?: true
    geocode?: true
    contenedor?: true
    servicio_codigo?: true
    status?: true
    nombre_cliente?: true
    documento_client?: true
    centro_costo?: true
    id_operador?: true
    codigo_operador?: true
    id_sede?: true
    nombre_sede?: true
    id_persona_jur_area?: true
    sector_direccion?: true
    tracking_tipo?: true
    created_at?: true
    updated_at?: true
    created_by?: true
    updated_by?: true
    start_geocode?: true
    address_normalized?: true
    address_problems?: true
    address_token?: true
    _all?: true
  }

  export type OlvaTrackingsAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which OlvaTrackings to aggregate.
     */
    where?: OlvaTrackingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of OlvaTrackings to fetch.
     */
    orderBy?: OlvaTrackingsOrderByWithRelationInput | OlvaTrackingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: OlvaTrackingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` OlvaTrackings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` OlvaTrackings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned OlvaTrackings
    **/
    _count?: true | OlvaTrackingsCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to average
    **/
    _avg?: OlvaTrackingsAvgAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to sum
    **/
    _sum?: OlvaTrackingsSumAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: OlvaTrackingsMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: OlvaTrackingsMaxAggregateInputType
  }

  export type GetOlvaTrackingsAggregateType<T extends OlvaTrackingsAggregateArgs> = {
        [P in keyof T & keyof AggregateOlvaTrackings]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateOlvaTrackings[P]>
      : GetScalarType<T[P], AggregateOlvaTrackings[P]>
  }




  export type OlvaTrackingsGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: OlvaTrackingsWhereInput
    orderBy?: OlvaTrackingsOrderByWithAggregationInput | OlvaTrackingsOrderByWithAggregationInput[]
    by: OlvaTrackingsScalarFieldEnum[] | OlvaTrackingsScalarFieldEnum
    having?: OlvaTrackingsScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: OlvaTrackingsCountAggregateInputType | true
    _avg?: OlvaTrackingsAvgAggregateInputType
    _sum?: OlvaTrackingsSumAggregateInputType
    _min?: OlvaTrackingsMinAggregateInputType
    _max?: OlvaTrackingsMaxAggregateInputType
  }

  export type OlvaTrackingsGroupByOutputType = {
    id: number
    id_envio: number
    address_id: number
    emision: number
    tracking: number
    address: string
    ubigeo: string | null
    cliente: number
    orden_servicio: string
    fecha_emision: string
    geocode: boolean
    contenedor: string
    servicio_codigo: string
    status: number
    nombre_cliente: string
    documento_client: string
    centro_costo: string
    id_operador: number
    codigo_operador: string
    id_sede: number
    nombre_sede: string
    id_persona_jur_area: number
    sector_direccion: string
    tracking_tipo: string
    created_at: number
    updated_at: number
    created_by: number
    updated_by: number
    start_geocode: number
    address_normalized: string
    address_problems: JsonValue
    address_token: string
    _count: OlvaTrackingsCountAggregateOutputType | null
    _avg: OlvaTrackingsAvgAggregateOutputType | null
    _sum: OlvaTrackingsSumAggregateOutputType | null
    _min: OlvaTrackingsMinAggregateOutputType | null
    _max: OlvaTrackingsMaxAggregateOutputType | null
  }

  type GetOlvaTrackingsGroupByPayload<T extends OlvaTrackingsGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<OlvaTrackingsGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof OlvaTrackingsGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], OlvaTrackingsGroupByOutputType[P]>
            : GetScalarType<T[P], OlvaTrackingsGroupByOutputType[P]>
        }
      >
    >


  export type OlvaTrackingsSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    id_envio?: boolean
    address_id?: boolean
    emision?: boolean
    tracking?: boolean
    address?: boolean
    ubigeo?: boolean
    cliente?: boolean
    orden_servicio?: boolean
    fecha_emision?: boolean
    geocode?: boolean
    contenedor?: boolean
    servicio_codigo?: boolean
    status?: boolean
    nombre_cliente?: boolean
    documento_client?: boolean
    centro_costo?: boolean
    id_operador?: boolean
    codigo_operador?: boolean
    id_sede?: boolean
    nombre_sede?: boolean
    id_persona_jur_area?: boolean
    sector_direccion?: boolean
    tracking_tipo?: boolean
    created_at?: boolean
    updated_at?: boolean
    created_by?: boolean
    updated_by?: boolean
    start_geocode?: boolean
    address_normalized?: boolean
    address_problems?: boolean
    address_token?: boolean
  }, ExtArgs["result"]["olvaTrackings"]>

  export type OlvaTrackingsSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    id_envio?: boolean
    address_id?: boolean
    emision?: boolean
    tracking?: boolean
    address?: boolean
    ubigeo?: boolean
    cliente?: boolean
    orden_servicio?: boolean
    fecha_emision?: boolean
    geocode?: boolean
    contenedor?: boolean
    servicio_codigo?: boolean
    status?: boolean
    nombre_cliente?: boolean
    documento_client?: boolean
    centro_costo?: boolean
    id_operador?: boolean
    codigo_operador?: boolean
    id_sede?: boolean
    nombre_sede?: boolean
    id_persona_jur_area?: boolean
    sector_direccion?: boolean
    tracking_tipo?: boolean
    created_at?: boolean
    updated_at?: boolean
    created_by?: boolean
    updated_by?: boolean
    start_geocode?: boolean
    address_normalized?: boolean
    address_problems?: boolean
    address_token?: boolean
  }, ExtArgs["result"]["olvaTrackings"]>

  export type OlvaTrackingsSelectUpdateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    id_envio?: boolean
    address_id?: boolean
    emision?: boolean
    tracking?: boolean
    address?: boolean
    ubigeo?: boolean
    cliente?: boolean
    orden_servicio?: boolean
    fecha_emision?: boolean
    geocode?: boolean
    contenedor?: boolean
    servicio_codigo?: boolean
    status?: boolean
    nombre_cliente?: boolean
    documento_client?: boolean
    centro_costo?: boolean
    id_operador?: boolean
    codigo_operador?: boolean
    id_sede?: boolean
    nombre_sede?: boolean
    id_persona_jur_area?: boolean
    sector_direccion?: boolean
    tracking_tipo?: boolean
    created_at?: boolean
    updated_at?: boolean
    created_by?: boolean
    updated_by?: boolean
    start_geocode?: boolean
    address_normalized?: boolean
    address_problems?: boolean
    address_token?: boolean
  }, ExtArgs["result"]["olvaTrackings"]>

  export type OlvaTrackingsSelectScalar = {
    id?: boolean
    id_envio?: boolean
    address_id?: boolean
    emision?: boolean
    tracking?: boolean
    address?: boolean
    ubigeo?: boolean
    cliente?: boolean
    orden_servicio?: boolean
    fecha_emision?: boolean
    geocode?: boolean
    contenedor?: boolean
    servicio_codigo?: boolean
    status?: boolean
    nombre_cliente?: boolean
    documento_client?: boolean
    centro_costo?: boolean
    id_operador?: boolean
    codigo_operador?: boolean
    id_sede?: boolean
    nombre_sede?: boolean
    id_persona_jur_area?: boolean
    sector_direccion?: boolean
    tracking_tipo?: boolean
    created_at?: boolean
    updated_at?: boolean
    created_by?: boolean
    updated_by?: boolean
    start_geocode?: boolean
    address_normalized?: boolean
    address_problems?: boolean
    address_token?: boolean
  }

  export type OlvaTrackingsOmit<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetOmit<"id" | "id_envio" | "address_id" | "emision" | "tracking" | "address" | "ubigeo" | "cliente" | "orden_servicio" | "fecha_emision" | "geocode" | "contenedor" | "servicio_codigo" | "status" | "nombre_cliente" | "documento_client" | "centro_costo" | "id_operador" | "codigo_operador" | "id_sede" | "nombre_sede" | "id_persona_jur_area" | "sector_direccion" | "tracking_tipo" | "created_at" | "updated_at" | "created_by" | "updated_by" | "start_geocode" | "address_normalized" | "address_problems" | "address_token", ExtArgs["result"]["olvaTrackings"]>

  export type $OlvaTrackingsPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "OlvaTrackings"
    objects: {}
    scalars: $Extensions.GetPayloadResult<{
      id: number
      id_envio: number
      address_id: number
      emision: number
      tracking: number
      address: string
      ubigeo: string | null
      cliente: number
      orden_servicio: string
      fecha_emision: string
      geocode: boolean
      contenedor: string
      servicio_codigo: string
      status: number
      nombre_cliente: string
      documento_client: string
      centro_costo: string
      id_operador: number
      codigo_operador: string
      id_sede: number
      nombre_sede: string
      id_persona_jur_area: number
      sector_direccion: string
      tracking_tipo: string
      created_at: number
      updated_at: number
      created_by: number
      updated_by: number
      start_geocode: number
      address_normalized: string
      address_problems: Prisma.JsonValue
      address_token: string
    }, ExtArgs["result"]["olvaTrackings"]>
    composites: {}
  }

  type OlvaTrackingsGetPayload<S extends boolean | null | undefined | OlvaTrackingsDefaultArgs> = $Result.GetResult<Prisma.$OlvaTrackingsPayload, S>

  type OlvaTrackingsCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> =
    Omit<OlvaTrackingsFindManyArgs, 'select' | 'include' | 'distinct' | 'omit'> & {
      select?: OlvaTrackingsCountAggregateInputType | true
    }

  export interface OlvaTrackingsDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['OlvaTrackings'], meta: { name: 'OlvaTrackings' } }
    /**
     * Find zero or one OlvaTrackings that matches the filter.
     * @param {OlvaTrackingsFindUniqueArgs} args - Arguments to find a OlvaTrackings
     * @example
     * // Get one OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends OlvaTrackingsFindUniqueArgs>(args: SelectSubset<T, OlvaTrackingsFindUniqueArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "findUnique", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find one OlvaTrackings that matches the filter or throw an error with `error.code='P2025'`
     * if no matches were found.
     * @param {OlvaTrackingsFindUniqueOrThrowArgs} args - Arguments to find a OlvaTrackings
     * @example
     * // Get one OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends OlvaTrackingsFindUniqueOrThrowArgs>(args: SelectSubset<T, OlvaTrackingsFindUniqueOrThrowArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "findUniqueOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first OlvaTrackings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {OlvaTrackingsFindFirstArgs} args - Arguments to find a OlvaTrackings
     * @example
     * // Get one OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends OlvaTrackingsFindFirstArgs>(args?: SelectSubset<T, OlvaTrackingsFindFirstArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "findFirst", GlobalOmitOptions> | null, null, ExtArgs, GlobalOmitOptions>

    /**
     * Find the first OlvaTrackings that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {OlvaTrackingsFindFirstOrThrowArgs} args - Arguments to find a OlvaTrackings
     * @example
     * // Get one OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends OlvaTrackingsFindFirstOrThrowArgs>(args?: SelectSubset<T, OlvaTrackingsFindFirstOrThrowArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "findFirstOrThrow", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Find zero or more OlvaTrackings that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {OlvaTrackingsFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.findMany()
     * 
     * // Get first 10 OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const olvaTrackingsWithIdOnly = await prisma.olvaTrackings.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends OlvaTrackingsFindManyArgs>(args?: SelectSubset<T, OlvaTrackingsFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "findMany", GlobalOmitOptions>>

    /**
     * Create a OlvaTrackings.
     * @param {OlvaTrackingsCreateArgs} args - Arguments to create a OlvaTrackings.
     * @example
     * // Create one OlvaTrackings
     * const OlvaTrackings = await prisma.olvaTrackings.create({
     *   data: {
     *     // ... data to create a OlvaTrackings
     *   }
     * })
     * 
     */
    create<T extends OlvaTrackingsCreateArgs>(args: SelectSubset<T, OlvaTrackingsCreateArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "create", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Create many OlvaTrackings.
     * @param {OlvaTrackingsCreateManyArgs} args - Arguments to create many OlvaTrackings.
     * @example
     * // Create many OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends OlvaTrackingsCreateManyArgs>(args?: SelectSubset<T, OlvaTrackingsCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many OlvaTrackings and returns the data saved in the database.
     * @param {OlvaTrackingsCreateManyAndReturnArgs} args - Arguments to create many OlvaTrackings.
     * @example
     * // Create many OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many OlvaTrackings and only return the `id`
     * const olvaTrackingsWithIdOnly = await prisma.olvaTrackings.createManyAndReturn({
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends OlvaTrackingsCreateManyAndReturnArgs>(args?: SelectSubset<T, OlvaTrackingsCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "createManyAndReturn", GlobalOmitOptions>>

    /**
     * Delete a OlvaTrackings.
     * @param {OlvaTrackingsDeleteArgs} args - Arguments to delete one OlvaTrackings.
     * @example
     * // Delete one OlvaTrackings
     * const OlvaTrackings = await prisma.olvaTrackings.delete({
     *   where: {
     *     // ... filter to delete one OlvaTrackings
     *   }
     * })
     * 
     */
    delete<T extends OlvaTrackingsDeleteArgs>(args: SelectSubset<T, OlvaTrackingsDeleteArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "delete", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Update one OlvaTrackings.
     * @param {OlvaTrackingsUpdateArgs} args - Arguments to update one OlvaTrackings.
     * @example
     * // Update one OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends OlvaTrackingsUpdateArgs>(args: SelectSubset<T, OlvaTrackingsUpdateArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "update", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>

    /**
     * Delete zero or more OlvaTrackings.
     * @param {OlvaTrackingsDeleteManyArgs} args - Arguments to filter OlvaTrackings to delete.
     * @example
     * // Delete a few OlvaTrackings
     * const { count } = await prisma.olvaTrackings.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends OlvaTrackingsDeleteManyArgs>(args?: SelectSubset<T, OlvaTrackingsDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more OlvaTrackings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {OlvaTrackingsUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends OlvaTrackingsUpdateManyArgs>(args: SelectSubset<T, OlvaTrackingsUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more OlvaTrackings and returns the data updated in the database.
     * @param {OlvaTrackingsUpdateManyAndReturnArgs} args - Arguments to update many OlvaTrackings.
     * @example
     * // Update many OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.updateManyAndReturn({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Update zero or more OlvaTrackings and only return the `id`
     * const olvaTrackingsWithIdOnly = await prisma.olvaTrackings.updateManyAndReturn({
     *   select: { id: true },
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    updateManyAndReturn<T extends OlvaTrackingsUpdateManyAndReturnArgs>(args: SelectSubset<T, OlvaTrackingsUpdateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "updateManyAndReturn", GlobalOmitOptions>>

    /**
     * Create or update one OlvaTrackings.
     * @param {OlvaTrackingsUpsertArgs} args - Arguments to update or create a OlvaTrackings.
     * @example
     * // Update or create a OlvaTrackings
     * const olvaTrackings = await prisma.olvaTrackings.upsert({
     *   create: {
     *     // ... data to create a OlvaTrackings
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the OlvaTrackings we want to update
     *   }
     * })
     */
    upsert<T extends OlvaTrackingsUpsertArgs>(args: SelectSubset<T, OlvaTrackingsUpsertArgs<ExtArgs>>): Prisma__OlvaTrackingsClient<$Result.GetResult<Prisma.$OlvaTrackingsPayload<ExtArgs>, T, "upsert", GlobalOmitOptions>, never, ExtArgs, GlobalOmitOptions>


    /**
     * Count the number of OlvaTrackings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {OlvaTrackingsCountArgs} args - Arguments to filter OlvaTrackings to count.
     * @example
     * // Count the number of OlvaTrackings
     * const count = await prisma.olvaTrackings.count({
     *   where: {
     *     // ... the filter for the OlvaTrackings we want to count
     *   }
     * })
    **/
    count<T extends OlvaTrackingsCountArgs>(
      args?: Subset<T, OlvaTrackingsCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], OlvaTrackingsCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a OlvaTrackings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {OlvaTrackingsAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends OlvaTrackingsAggregateArgs>(args: Subset<T, OlvaTrackingsAggregateArgs>): Prisma.PrismaPromise<GetOlvaTrackingsAggregateType<T>>

    /**
     * Group by OlvaTrackings.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {OlvaTrackingsGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends OlvaTrackingsGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: OlvaTrackingsGroupByArgs['orderBy'] }
        : { orderBy?: OlvaTrackingsGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, OlvaTrackingsGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetOlvaTrackingsGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the OlvaTrackings model
   */
  readonly fields: OlvaTrackingsFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for OlvaTrackings.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__OlvaTrackingsClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, GlobalOmitOptions = {}> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the OlvaTrackings model
   */
  interface OlvaTrackingsFieldRefs {
    readonly id: FieldRef<"OlvaTrackings", 'Int'>
    readonly id_envio: FieldRef<"OlvaTrackings", 'Int'>
    readonly address_id: FieldRef<"OlvaTrackings", 'Int'>
    readonly emision: FieldRef<"OlvaTrackings", 'Int'>
    readonly tracking: FieldRef<"OlvaTrackings", 'Int'>
    readonly address: FieldRef<"OlvaTrackings", 'String'>
    readonly ubigeo: FieldRef<"OlvaTrackings", 'String'>
    readonly cliente: FieldRef<"OlvaTrackings", 'Int'>
    readonly orden_servicio: FieldRef<"OlvaTrackings", 'String'>
    readonly fecha_emision: FieldRef<"OlvaTrackings", 'String'>
    readonly geocode: FieldRef<"OlvaTrackings", 'Boolean'>
    readonly contenedor: FieldRef<"OlvaTrackings", 'String'>
    readonly servicio_codigo: FieldRef<"OlvaTrackings", 'String'>
    readonly status: FieldRef<"OlvaTrackings", 'Int'>
    readonly nombre_cliente: FieldRef<"OlvaTrackings", 'String'>
    readonly documento_client: FieldRef<"OlvaTrackings", 'String'>
    readonly centro_costo: FieldRef<"OlvaTrackings", 'String'>
    readonly id_operador: FieldRef<"OlvaTrackings", 'Int'>
    readonly codigo_operador: FieldRef<"OlvaTrackings", 'String'>
    readonly id_sede: FieldRef<"OlvaTrackings", 'Int'>
    readonly nombre_sede: FieldRef<"OlvaTrackings", 'String'>
    readonly id_persona_jur_area: FieldRef<"OlvaTrackings", 'Int'>
    readonly sector_direccion: FieldRef<"OlvaTrackings", 'String'>
    readonly tracking_tipo: FieldRef<"OlvaTrackings", 'String'>
    readonly created_at: FieldRef<"OlvaTrackings", 'Float'>
    readonly updated_at: FieldRef<"OlvaTrackings", 'Float'>
    readonly created_by: FieldRef<"OlvaTrackings", 'Int'>
    readonly updated_by: FieldRef<"OlvaTrackings", 'Int'>
    readonly start_geocode: FieldRef<"OlvaTrackings", 'Int'>
    readonly address_normalized: FieldRef<"OlvaTrackings", 'String'>
    readonly address_problems: FieldRef<"OlvaTrackings", 'Json'>
    readonly address_token: FieldRef<"OlvaTrackings", 'String'>
  }
    

  // Custom InputTypes
  /**
   * OlvaTrackings findUnique
   */
  export type OlvaTrackingsFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * Filter, which OlvaTrackings to fetch.
     */
    where: OlvaTrackingsWhereUniqueInput
  }

  /**
   * OlvaTrackings findUniqueOrThrow
   */
  export type OlvaTrackingsFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * Filter, which OlvaTrackings to fetch.
     */
    where: OlvaTrackingsWhereUniqueInput
  }

  /**
   * OlvaTrackings findFirst
   */
  export type OlvaTrackingsFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * Filter, which OlvaTrackings to fetch.
     */
    where?: OlvaTrackingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of OlvaTrackings to fetch.
     */
    orderBy?: OlvaTrackingsOrderByWithRelationInput | OlvaTrackingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for OlvaTrackings.
     */
    cursor?: OlvaTrackingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` OlvaTrackings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` OlvaTrackings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of OlvaTrackings.
     */
    distinct?: OlvaTrackingsScalarFieldEnum | OlvaTrackingsScalarFieldEnum[]
  }

  /**
   * OlvaTrackings findFirstOrThrow
   */
  export type OlvaTrackingsFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * Filter, which OlvaTrackings to fetch.
     */
    where?: OlvaTrackingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of OlvaTrackings to fetch.
     */
    orderBy?: OlvaTrackingsOrderByWithRelationInput | OlvaTrackingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for OlvaTrackings.
     */
    cursor?: OlvaTrackingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` OlvaTrackings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` OlvaTrackings.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of OlvaTrackings.
     */
    distinct?: OlvaTrackingsScalarFieldEnum | OlvaTrackingsScalarFieldEnum[]
  }

  /**
   * OlvaTrackings findMany
   */
  export type OlvaTrackingsFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * Filter, which OlvaTrackings to fetch.
     */
    where?: OlvaTrackingsWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of OlvaTrackings to fetch.
     */
    orderBy?: OlvaTrackingsOrderByWithRelationInput | OlvaTrackingsOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing OlvaTrackings.
     */
    cursor?: OlvaTrackingsWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` OlvaTrackings from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` OlvaTrackings.
     */
    skip?: number
    distinct?: OlvaTrackingsScalarFieldEnum | OlvaTrackingsScalarFieldEnum[]
  }

  /**
   * OlvaTrackings create
   */
  export type OlvaTrackingsCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * The data needed to create a OlvaTrackings.
     */
    data: XOR<OlvaTrackingsCreateInput, OlvaTrackingsUncheckedCreateInput>
  }

  /**
   * OlvaTrackings createMany
   */
  export type OlvaTrackingsCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many OlvaTrackings.
     */
    data: OlvaTrackingsCreateManyInput | OlvaTrackingsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * OlvaTrackings createManyAndReturn
   */
  export type OlvaTrackingsCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * The data used to create many OlvaTrackings.
     */
    data: OlvaTrackingsCreateManyInput | OlvaTrackingsCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * OlvaTrackings update
   */
  export type OlvaTrackingsUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * The data needed to update a OlvaTrackings.
     */
    data: XOR<OlvaTrackingsUpdateInput, OlvaTrackingsUncheckedUpdateInput>
    /**
     * Choose, which OlvaTrackings to update.
     */
    where: OlvaTrackingsWhereUniqueInput
  }

  /**
   * OlvaTrackings updateMany
   */
  export type OlvaTrackingsUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update OlvaTrackings.
     */
    data: XOR<OlvaTrackingsUpdateManyMutationInput, OlvaTrackingsUncheckedUpdateManyInput>
    /**
     * Filter which OlvaTrackings to update
     */
    where?: OlvaTrackingsWhereInput
    /**
     * Limit how many OlvaTrackings to update.
     */
    limit?: number
  }

  /**
   * OlvaTrackings updateManyAndReturn
   */
  export type OlvaTrackingsUpdateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelectUpdateManyAndReturn<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * The data used to update OlvaTrackings.
     */
    data: XOR<OlvaTrackingsUpdateManyMutationInput, OlvaTrackingsUncheckedUpdateManyInput>
    /**
     * Filter which OlvaTrackings to update
     */
    where?: OlvaTrackingsWhereInput
    /**
     * Limit how many OlvaTrackings to update.
     */
    limit?: number
  }

  /**
   * OlvaTrackings upsert
   */
  export type OlvaTrackingsUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * The filter to search for the OlvaTrackings to update in case it exists.
     */
    where: OlvaTrackingsWhereUniqueInput
    /**
     * In case the OlvaTrackings found by the `where` argument doesn't exist, create a new OlvaTrackings with this data.
     */
    create: XOR<OlvaTrackingsCreateInput, OlvaTrackingsUncheckedCreateInput>
    /**
     * In case the OlvaTrackings was found with the provided `where` argument, update it with this data.
     */
    update: XOR<OlvaTrackingsUpdateInput, OlvaTrackingsUncheckedUpdateInput>
  }

  /**
   * OlvaTrackings delete
   */
  export type OlvaTrackingsDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
    /**
     * Filter which OlvaTrackings to delete.
     */
    where: OlvaTrackingsWhereUniqueInput
  }

  /**
   * OlvaTrackings deleteMany
   */
  export type OlvaTrackingsDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which OlvaTrackings to delete
     */
    where?: OlvaTrackingsWhereInput
    /**
     * Limit how many OlvaTrackings to delete.
     */
    limit?: number
  }

  /**
   * OlvaTrackings without action
   */
  export type OlvaTrackingsDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the OlvaTrackings
     */
    select?: OlvaTrackingsSelect<ExtArgs> | null
    /**
     * Omit specific fields from the OlvaTrackings
     */
    omit?: OlvaTrackingsOmit<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const OlvaTrackingsScalarFieldEnum: {
    id: 'id',
    id_envio: 'id_envio',
    address_id: 'address_id',
    emision: 'emision',
    tracking: 'tracking',
    address: 'address',
    ubigeo: 'ubigeo',
    cliente: 'cliente',
    orden_servicio: 'orden_servicio',
    fecha_emision: 'fecha_emision',
    geocode: 'geocode',
    contenedor: 'contenedor',
    servicio_codigo: 'servicio_codigo',
    status: 'status',
    nombre_cliente: 'nombre_cliente',
    documento_client: 'documento_client',
    centro_costo: 'centro_costo',
    id_operador: 'id_operador',
    codigo_operador: 'codigo_operador',
    id_sede: 'id_sede',
    nombre_sede: 'nombre_sede',
    id_persona_jur_area: 'id_persona_jur_area',
    sector_direccion: 'sector_direccion',
    tracking_tipo: 'tracking_tipo',
    created_at: 'created_at',
    updated_at: 'updated_at',
    created_by: 'created_by',
    updated_by: 'updated_by',
    start_geocode: 'start_geocode',
    address_normalized: 'address_normalized',
    address_problems: 'address_problems',
    address_token: 'address_token'
  };

  export type OlvaTrackingsScalarFieldEnum = (typeof OlvaTrackingsScalarFieldEnum)[keyof typeof OlvaTrackingsScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  /**
   * Field references
   */


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'Float'
   */
  export type FloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float'>
    


  /**
   * Reference to a field of type 'Float[]'
   */
  export type ListFloatFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Float[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'QueryMode'
   */
  export type EnumQueryModeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'QueryMode'>
    
  /**
   * Deep Input Types
   */


  export type OlvaTrackingsWhereInput = {
    AND?: OlvaTrackingsWhereInput | OlvaTrackingsWhereInput[]
    OR?: OlvaTrackingsWhereInput[]
    NOT?: OlvaTrackingsWhereInput | OlvaTrackingsWhereInput[]
    id?: IntFilter<"OlvaTrackings"> | number
    id_envio?: IntFilter<"OlvaTrackings"> | number
    address_id?: IntFilter<"OlvaTrackings"> | number
    emision?: IntFilter<"OlvaTrackings"> | number
    tracking?: IntFilter<"OlvaTrackings"> | number
    address?: StringFilter<"OlvaTrackings"> | string
    ubigeo?: StringNullableFilter<"OlvaTrackings"> | string | null
    cliente?: IntFilter<"OlvaTrackings"> | number
    orden_servicio?: StringFilter<"OlvaTrackings"> | string
    fecha_emision?: StringFilter<"OlvaTrackings"> | string
    geocode?: BoolFilter<"OlvaTrackings"> | boolean
    contenedor?: StringFilter<"OlvaTrackings"> | string
    servicio_codigo?: StringFilter<"OlvaTrackings"> | string
    status?: IntFilter<"OlvaTrackings"> | number
    nombre_cliente?: StringFilter<"OlvaTrackings"> | string
    documento_client?: StringFilter<"OlvaTrackings"> | string
    centro_costo?: StringFilter<"OlvaTrackings"> | string
    id_operador?: IntFilter<"OlvaTrackings"> | number
    codigo_operador?: StringFilter<"OlvaTrackings"> | string
    id_sede?: IntFilter<"OlvaTrackings"> | number
    nombre_sede?: StringFilter<"OlvaTrackings"> | string
    id_persona_jur_area?: IntFilter<"OlvaTrackings"> | number
    sector_direccion?: StringFilter<"OlvaTrackings"> | string
    tracking_tipo?: StringFilter<"OlvaTrackings"> | string
    created_at?: FloatFilter<"OlvaTrackings"> | number
    updated_at?: FloatFilter<"OlvaTrackings"> | number
    created_by?: IntFilter<"OlvaTrackings"> | number
    updated_by?: IntFilter<"OlvaTrackings"> | number
    start_geocode?: IntFilter<"OlvaTrackings"> | number
    address_normalized?: StringFilter<"OlvaTrackings"> | string
    address_problems?: JsonFilter<"OlvaTrackings">
    address_token?: StringFilter<"OlvaTrackings"> | string
  }

  export type OlvaTrackingsOrderByWithRelationInput = {
    id?: SortOrder
    id_envio?: SortOrder
    address_id?: SortOrder
    emision?: SortOrder
    tracking?: SortOrder
    address?: SortOrder
    ubigeo?: SortOrderInput | SortOrder
    cliente?: SortOrder
    orden_servicio?: SortOrder
    fecha_emision?: SortOrder
    geocode?: SortOrder
    contenedor?: SortOrder
    servicio_codigo?: SortOrder
    status?: SortOrder
    nombre_cliente?: SortOrder
    documento_client?: SortOrder
    centro_costo?: SortOrder
    id_operador?: SortOrder
    codigo_operador?: SortOrder
    id_sede?: SortOrder
    nombre_sede?: SortOrder
    id_persona_jur_area?: SortOrder
    sector_direccion?: SortOrder
    tracking_tipo?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    created_by?: SortOrder
    updated_by?: SortOrder
    start_geocode?: SortOrder
    address_normalized?: SortOrder
    address_problems?: SortOrder
    address_token?: SortOrder
  }

  export type OlvaTrackingsWhereUniqueInput = Prisma.AtLeast<{
    id?: number
    id_envio?: number
    AND?: OlvaTrackingsWhereInput | OlvaTrackingsWhereInput[]
    OR?: OlvaTrackingsWhereInput[]
    NOT?: OlvaTrackingsWhereInput | OlvaTrackingsWhereInput[]
    address_id?: IntFilter<"OlvaTrackings"> | number
    emision?: IntFilter<"OlvaTrackings"> | number
    tracking?: IntFilter<"OlvaTrackings"> | number
    address?: StringFilter<"OlvaTrackings"> | string
    ubigeo?: StringNullableFilter<"OlvaTrackings"> | string | null
    cliente?: IntFilter<"OlvaTrackings"> | number
    orden_servicio?: StringFilter<"OlvaTrackings"> | string
    fecha_emision?: StringFilter<"OlvaTrackings"> | string
    geocode?: BoolFilter<"OlvaTrackings"> | boolean
    contenedor?: StringFilter<"OlvaTrackings"> | string
    servicio_codigo?: StringFilter<"OlvaTrackings"> | string
    status?: IntFilter<"OlvaTrackings"> | number
    nombre_cliente?: StringFilter<"OlvaTrackings"> | string
    documento_client?: StringFilter<"OlvaTrackings"> | string
    centro_costo?: StringFilter<"OlvaTrackings"> | string
    id_operador?: IntFilter<"OlvaTrackings"> | number
    codigo_operador?: StringFilter<"OlvaTrackings"> | string
    id_sede?: IntFilter<"OlvaTrackings"> | number
    nombre_sede?: StringFilter<"OlvaTrackings"> | string
    id_persona_jur_area?: IntFilter<"OlvaTrackings"> | number
    sector_direccion?: StringFilter<"OlvaTrackings"> | string
    tracking_tipo?: StringFilter<"OlvaTrackings"> | string
    created_at?: FloatFilter<"OlvaTrackings"> | number
    updated_at?: FloatFilter<"OlvaTrackings"> | number
    created_by?: IntFilter<"OlvaTrackings"> | number
    updated_by?: IntFilter<"OlvaTrackings"> | number
    start_geocode?: IntFilter<"OlvaTrackings"> | number
    address_normalized?: StringFilter<"OlvaTrackings"> | string
    address_problems?: JsonFilter<"OlvaTrackings">
    address_token?: StringFilter<"OlvaTrackings"> | string
  }, "id" | "id_envio">

  export type OlvaTrackingsOrderByWithAggregationInput = {
    id?: SortOrder
    id_envio?: SortOrder
    address_id?: SortOrder
    emision?: SortOrder
    tracking?: SortOrder
    address?: SortOrder
    ubigeo?: SortOrderInput | SortOrder
    cliente?: SortOrder
    orden_servicio?: SortOrder
    fecha_emision?: SortOrder
    geocode?: SortOrder
    contenedor?: SortOrder
    servicio_codigo?: SortOrder
    status?: SortOrder
    nombre_cliente?: SortOrder
    documento_client?: SortOrder
    centro_costo?: SortOrder
    id_operador?: SortOrder
    codigo_operador?: SortOrder
    id_sede?: SortOrder
    nombre_sede?: SortOrder
    id_persona_jur_area?: SortOrder
    sector_direccion?: SortOrder
    tracking_tipo?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    created_by?: SortOrder
    updated_by?: SortOrder
    start_geocode?: SortOrder
    address_normalized?: SortOrder
    address_problems?: SortOrder
    address_token?: SortOrder
    _count?: OlvaTrackingsCountOrderByAggregateInput
    _avg?: OlvaTrackingsAvgOrderByAggregateInput
    _max?: OlvaTrackingsMaxOrderByAggregateInput
    _min?: OlvaTrackingsMinOrderByAggregateInput
    _sum?: OlvaTrackingsSumOrderByAggregateInput
  }

  export type OlvaTrackingsScalarWhereWithAggregatesInput = {
    AND?: OlvaTrackingsScalarWhereWithAggregatesInput | OlvaTrackingsScalarWhereWithAggregatesInput[]
    OR?: OlvaTrackingsScalarWhereWithAggregatesInput[]
    NOT?: OlvaTrackingsScalarWhereWithAggregatesInput | OlvaTrackingsScalarWhereWithAggregatesInput[]
    id?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    id_envio?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    address_id?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    emision?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    tracking?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    address?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    ubigeo?: StringNullableWithAggregatesFilter<"OlvaTrackings"> | string | null
    cliente?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    orden_servicio?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    fecha_emision?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    geocode?: BoolWithAggregatesFilter<"OlvaTrackings"> | boolean
    contenedor?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    servicio_codigo?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    status?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    nombre_cliente?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    documento_client?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    centro_costo?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    id_operador?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    codigo_operador?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    id_sede?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    nombre_sede?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    id_persona_jur_area?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    sector_direccion?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    tracking_tipo?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    created_at?: FloatWithAggregatesFilter<"OlvaTrackings"> | number
    updated_at?: FloatWithAggregatesFilter<"OlvaTrackings"> | number
    created_by?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    updated_by?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    start_geocode?: IntWithAggregatesFilter<"OlvaTrackings"> | number
    address_normalized?: StringWithAggregatesFilter<"OlvaTrackings"> | string
    address_problems?: JsonWithAggregatesFilter<"OlvaTrackings">
    address_token?: StringWithAggregatesFilter<"OlvaTrackings"> | string
  }

  export type OlvaTrackingsCreateInput = {
    id: number
    id_envio: number
    address_id: number
    emision: number
    tracking: number
    address: string
    ubigeo?: string | null
    cliente: number
    orden_servicio: string
    fecha_emision: string
    geocode: boolean
    contenedor: string
    servicio_codigo: string
    status: number
    nombre_cliente: string
    documento_client: string
    centro_costo: string
    id_operador: number
    codigo_operador: string
    id_sede: number
    nombre_sede: string
    id_persona_jur_area: number
    sector_direccion: string
    tracking_tipo: string
    created_at: number
    updated_at: number
    created_by: number
    updated_by: number
    start_geocode: number
    address_normalized: string
    address_problems: JsonNullValueInput | InputJsonValue
    address_token: string
  }

  export type OlvaTrackingsUncheckedCreateInput = {
    id: number
    id_envio: number
    address_id: number
    emision: number
    tracking: number
    address: string
    ubigeo?: string | null
    cliente: number
    orden_servicio: string
    fecha_emision: string
    geocode: boolean
    contenedor: string
    servicio_codigo: string
    status: number
    nombre_cliente: string
    documento_client: string
    centro_costo: string
    id_operador: number
    codigo_operador: string
    id_sede: number
    nombre_sede: string
    id_persona_jur_area: number
    sector_direccion: string
    tracking_tipo: string
    created_at: number
    updated_at: number
    created_by: number
    updated_by: number
    start_geocode: number
    address_normalized: string
    address_problems: JsonNullValueInput | InputJsonValue
    address_token: string
  }

  export type OlvaTrackingsUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    id_envio?: IntFieldUpdateOperationsInput | number
    address_id?: IntFieldUpdateOperationsInput | number
    emision?: IntFieldUpdateOperationsInput | number
    tracking?: IntFieldUpdateOperationsInput | number
    address?: StringFieldUpdateOperationsInput | string
    ubigeo?: NullableStringFieldUpdateOperationsInput | string | null
    cliente?: IntFieldUpdateOperationsInput | number
    orden_servicio?: StringFieldUpdateOperationsInput | string
    fecha_emision?: StringFieldUpdateOperationsInput | string
    geocode?: BoolFieldUpdateOperationsInput | boolean
    contenedor?: StringFieldUpdateOperationsInput | string
    servicio_codigo?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    nombre_cliente?: StringFieldUpdateOperationsInput | string
    documento_client?: StringFieldUpdateOperationsInput | string
    centro_costo?: StringFieldUpdateOperationsInput | string
    id_operador?: IntFieldUpdateOperationsInput | number
    codigo_operador?: StringFieldUpdateOperationsInput | string
    id_sede?: IntFieldUpdateOperationsInput | number
    nombre_sede?: StringFieldUpdateOperationsInput | string
    id_persona_jur_area?: IntFieldUpdateOperationsInput | number
    sector_direccion?: StringFieldUpdateOperationsInput | string
    tracking_tipo?: StringFieldUpdateOperationsInput | string
    created_at?: FloatFieldUpdateOperationsInput | number
    updated_at?: FloatFieldUpdateOperationsInput | number
    created_by?: IntFieldUpdateOperationsInput | number
    updated_by?: IntFieldUpdateOperationsInput | number
    start_geocode?: IntFieldUpdateOperationsInput | number
    address_normalized?: StringFieldUpdateOperationsInput | string
    address_problems?: JsonNullValueInput | InputJsonValue
    address_token?: StringFieldUpdateOperationsInput | string
  }

  export type OlvaTrackingsUncheckedUpdateInput = {
    id?: IntFieldUpdateOperationsInput | number
    id_envio?: IntFieldUpdateOperationsInput | number
    address_id?: IntFieldUpdateOperationsInput | number
    emision?: IntFieldUpdateOperationsInput | number
    tracking?: IntFieldUpdateOperationsInput | number
    address?: StringFieldUpdateOperationsInput | string
    ubigeo?: NullableStringFieldUpdateOperationsInput | string | null
    cliente?: IntFieldUpdateOperationsInput | number
    orden_servicio?: StringFieldUpdateOperationsInput | string
    fecha_emision?: StringFieldUpdateOperationsInput | string
    geocode?: BoolFieldUpdateOperationsInput | boolean
    contenedor?: StringFieldUpdateOperationsInput | string
    servicio_codigo?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    nombre_cliente?: StringFieldUpdateOperationsInput | string
    documento_client?: StringFieldUpdateOperationsInput | string
    centro_costo?: StringFieldUpdateOperationsInput | string
    id_operador?: IntFieldUpdateOperationsInput | number
    codigo_operador?: StringFieldUpdateOperationsInput | string
    id_sede?: IntFieldUpdateOperationsInput | number
    nombre_sede?: StringFieldUpdateOperationsInput | string
    id_persona_jur_area?: IntFieldUpdateOperationsInput | number
    sector_direccion?: StringFieldUpdateOperationsInput | string
    tracking_tipo?: StringFieldUpdateOperationsInput | string
    created_at?: FloatFieldUpdateOperationsInput | number
    updated_at?: FloatFieldUpdateOperationsInput | number
    created_by?: IntFieldUpdateOperationsInput | number
    updated_by?: IntFieldUpdateOperationsInput | number
    start_geocode?: IntFieldUpdateOperationsInput | number
    address_normalized?: StringFieldUpdateOperationsInput | string
    address_problems?: JsonNullValueInput | InputJsonValue
    address_token?: StringFieldUpdateOperationsInput | string
  }

  export type OlvaTrackingsCreateManyInput = {
    id: number
    id_envio: number
    address_id: number
    emision: number
    tracking: number
    address: string
    ubigeo?: string | null
    cliente: number
    orden_servicio: string
    fecha_emision: string
    geocode: boolean
    contenedor: string
    servicio_codigo: string
    status: number
    nombre_cliente: string
    documento_client: string
    centro_costo: string
    id_operador: number
    codigo_operador: string
    id_sede: number
    nombre_sede: string
    id_persona_jur_area: number
    sector_direccion: string
    tracking_tipo: string
    created_at: number
    updated_at: number
    created_by: number
    updated_by: number
    start_geocode: number
    address_normalized: string
    address_problems: JsonNullValueInput | InputJsonValue
    address_token: string
  }

  export type OlvaTrackingsUpdateManyMutationInput = {
    id?: IntFieldUpdateOperationsInput | number
    id_envio?: IntFieldUpdateOperationsInput | number
    address_id?: IntFieldUpdateOperationsInput | number
    emision?: IntFieldUpdateOperationsInput | number
    tracking?: IntFieldUpdateOperationsInput | number
    address?: StringFieldUpdateOperationsInput | string
    ubigeo?: NullableStringFieldUpdateOperationsInput | string | null
    cliente?: IntFieldUpdateOperationsInput | number
    orden_servicio?: StringFieldUpdateOperationsInput | string
    fecha_emision?: StringFieldUpdateOperationsInput | string
    geocode?: BoolFieldUpdateOperationsInput | boolean
    contenedor?: StringFieldUpdateOperationsInput | string
    servicio_codigo?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    nombre_cliente?: StringFieldUpdateOperationsInput | string
    documento_client?: StringFieldUpdateOperationsInput | string
    centro_costo?: StringFieldUpdateOperationsInput | string
    id_operador?: IntFieldUpdateOperationsInput | number
    codigo_operador?: StringFieldUpdateOperationsInput | string
    id_sede?: IntFieldUpdateOperationsInput | number
    nombre_sede?: StringFieldUpdateOperationsInput | string
    id_persona_jur_area?: IntFieldUpdateOperationsInput | number
    sector_direccion?: StringFieldUpdateOperationsInput | string
    tracking_tipo?: StringFieldUpdateOperationsInput | string
    created_at?: FloatFieldUpdateOperationsInput | number
    updated_at?: FloatFieldUpdateOperationsInput | number
    created_by?: IntFieldUpdateOperationsInput | number
    updated_by?: IntFieldUpdateOperationsInput | number
    start_geocode?: IntFieldUpdateOperationsInput | number
    address_normalized?: StringFieldUpdateOperationsInput | string
    address_problems?: JsonNullValueInput | InputJsonValue
    address_token?: StringFieldUpdateOperationsInput | string
  }

  export type OlvaTrackingsUncheckedUpdateManyInput = {
    id?: IntFieldUpdateOperationsInput | number
    id_envio?: IntFieldUpdateOperationsInput | number
    address_id?: IntFieldUpdateOperationsInput | number
    emision?: IntFieldUpdateOperationsInput | number
    tracking?: IntFieldUpdateOperationsInput | number
    address?: StringFieldUpdateOperationsInput | string
    ubigeo?: NullableStringFieldUpdateOperationsInput | string | null
    cliente?: IntFieldUpdateOperationsInput | number
    orden_servicio?: StringFieldUpdateOperationsInput | string
    fecha_emision?: StringFieldUpdateOperationsInput | string
    geocode?: BoolFieldUpdateOperationsInput | boolean
    contenedor?: StringFieldUpdateOperationsInput | string
    servicio_codigo?: StringFieldUpdateOperationsInput | string
    status?: IntFieldUpdateOperationsInput | number
    nombre_cliente?: StringFieldUpdateOperationsInput | string
    documento_client?: StringFieldUpdateOperationsInput | string
    centro_costo?: StringFieldUpdateOperationsInput | string
    id_operador?: IntFieldUpdateOperationsInput | number
    codigo_operador?: StringFieldUpdateOperationsInput | string
    id_sede?: IntFieldUpdateOperationsInput | number
    nombre_sede?: StringFieldUpdateOperationsInput | string
    id_persona_jur_area?: IntFieldUpdateOperationsInput | number
    sector_direccion?: StringFieldUpdateOperationsInput | string
    tracking_tipo?: StringFieldUpdateOperationsInput | string
    created_at?: FloatFieldUpdateOperationsInput | number
    updated_at?: FloatFieldUpdateOperationsInput | number
    created_by?: IntFieldUpdateOperationsInput | number
    updated_by?: IntFieldUpdateOperationsInput | number
    start_geocode?: IntFieldUpdateOperationsInput | number
    address_normalized?: StringFieldUpdateOperationsInput | string
    address_problems?: JsonNullValueInput | InputJsonValue
    address_token?: StringFieldUpdateOperationsInput | string
  }

  export type IntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type FloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }
  export type JsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type OlvaTrackingsCountOrderByAggregateInput = {
    id?: SortOrder
    id_envio?: SortOrder
    address_id?: SortOrder
    emision?: SortOrder
    tracking?: SortOrder
    address?: SortOrder
    ubigeo?: SortOrder
    cliente?: SortOrder
    orden_servicio?: SortOrder
    fecha_emision?: SortOrder
    geocode?: SortOrder
    contenedor?: SortOrder
    servicio_codigo?: SortOrder
    status?: SortOrder
    nombre_cliente?: SortOrder
    documento_client?: SortOrder
    centro_costo?: SortOrder
    id_operador?: SortOrder
    codigo_operador?: SortOrder
    id_sede?: SortOrder
    nombre_sede?: SortOrder
    id_persona_jur_area?: SortOrder
    sector_direccion?: SortOrder
    tracking_tipo?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    created_by?: SortOrder
    updated_by?: SortOrder
    start_geocode?: SortOrder
    address_normalized?: SortOrder
    address_problems?: SortOrder
    address_token?: SortOrder
  }

  export type OlvaTrackingsAvgOrderByAggregateInput = {
    id?: SortOrder
    id_envio?: SortOrder
    address_id?: SortOrder
    emision?: SortOrder
    tracking?: SortOrder
    cliente?: SortOrder
    status?: SortOrder
    id_operador?: SortOrder
    id_sede?: SortOrder
    id_persona_jur_area?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    created_by?: SortOrder
    updated_by?: SortOrder
    start_geocode?: SortOrder
  }

  export type OlvaTrackingsMaxOrderByAggregateInput = {
    id?: SortOrder
    id_envio?: SortOrder
    address_id?: SortOrder
    emision?: SortOrder
    tracking?: SortOrder
    address?: SortOrder
    ubigeo?: SortOrder
    cliente?: SortOrder
    orden_servicio?: SortOrder
    fecha_emision?: SortOrder
    geocode?: SortOrder
    contenedor?: SortOrder
    servicio_codigo?: SortOrder
    status?: SortOrder
    nombre_cliente?: SortOrder
    documento_client?: SortOrder
    centro_costo?: SortOrder
    id_operador?: SortOrder
    codigo_operador?: SortOrder
    id_sede?: SortOrder
    nombre_sede?: SortOrder
    id_persona_jur_area?: SortOrder
    sector_direccion?: SortOrder
    tracking_tipo?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    created_by?: SortOrder
    updated_by?: SortOrder
    start_geocode?: SortOrder
    address_normalized?: SortOrder
    address_token?: SortOrder
  }

  export type OlvaTrackingsMinOrderByAggregateInput = {
    id?: SortOrder
    id_envio?: SortOrder
    address_id?: SortOrder
    emision?: SortOrder
    tracking?: SortOrder
    address?: SortOrder
    ubigeo?: SortOrder
    cliente?: SortOrder
    orden_servicio?: SortOrder
    fecha_emision?: SortOrder
    geocode?: SortOrder
    contenedor?: SortOrder
    servicio_codigo?: SortOrder
    status?: SortOrder
    nombre_cliente?: SortOrder
    documento_client?: SortOrder
    centro_costo?: SortOrder
    id_operador?: SortOrder
    codigo_operador?: SortOrder
    id_sede?: SortOrder
    nombre_sede?: SortOrder
    id_persona_jur_area?: SortOrder
    sector_direccion?: SortOrder
    tracking_tipo?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    created_by?: SortOrder
    updated_by?: SortOrder
    start_geocode?: SortOrder
    address_normalized?: SortOrder
    address_token?: SortOrder
  }

  export type OlvaTrackingsSumOrderByAggregateInput = {
    id?: SortOrder
    id_envio?: SortOrder
    address_id?: SortOrder
    emision?: SortOrder
    tracking?: SortOrder
    cliente?: SortOrder
    status?: SortOrder
    id_operador?: SortOrder
    id_sede?: SortOrder
    id_persona_jur_area?: SortOrder
    created_at?: SortOrder
    updated_at?: SortOrder
    created_by?: SortOrder
    updated_by?: SortOrder
    start_geocode?: SortOrder
  }

  export type IntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type FloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }

  export type IntFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type FloatFieldUpdateOperationsInput = {
    set?: number
    increment?: number
    decrement?: number
    multiply?: number
    divide?: number
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedFloatFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatFilter<$PrismaModel> | number
  }

  export type NestedIntWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedIntFilter<$PrismaModel>
    _min?: NestedIntFilter<$PrismaModel>
    _max?: NestedIntFilter<$PrismaModel>
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedFloatWithAggregatesFilter<$PrismaModel = never> = {
    equals?: number | FloatFieldRefInput<$PrismaModel>
    in?: number[] | ListFloatFieldRefInput<$PrismaModel>
    notIn?: number[] | ListFloatFieldRefInput<$PrismaModel>
    lt?: number | FloatFieldRefInput<$PrismaModel>
    lte?: number | FloatFieldRefInput<$PrismaModel>
    gt?: number | FloatFieldRefInput<$PrismaModel>
    gte?: number | FloatFieldRefInput<$PrismaModel>
    not?: NestedFloatWithAggregatesFilter<$PrismaModel> | number
    _count?: NestedIntFilter<$PrismaModel>
    _avg?: NestedFloatFilter<$PrismaModel>
    _sum?: NestedFloatFilter<$PrismaModel>
    _min?: NestedFloatFilter<$PrismaModel>
    _max?: NestedFloatFilter<$PrismaModel>
  }
  export type NestedJsonFilter<$PrismaModel = never> =
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    mode?: QueryMode | EnumQueryModeFieldRefInput<$PrismaModel>
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }



  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}